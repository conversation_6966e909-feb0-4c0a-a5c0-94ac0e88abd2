{"name": "passport-local", "version": "1.0.0", "description": "Local username and password authentication strategy for Passport.", "keywords": ["passport", "local", "auth", "authn", "authentication", "username", "password"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "repository": {"type": "git", "url": "git://github.com/jaredhanson/passport-local.git"}, "bugs": {"url": "http://github.com/jaredhanson/passport-local/issues"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"passport-strategy": "1.x.x"}, "devDependencies": {"mocha": "1.x.x", "chai": "1.x.x", "chai-passport-strategy": "0.1.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "node_modules/.bin/mocha --reporter spec --require test/bootstrap/node test/*.test.js"}}