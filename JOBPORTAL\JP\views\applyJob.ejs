<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Application</title>
  </head>
  <body>
    <h2>Apply for <%= job.title %> at <%= job.company.name %></h2>
    <form action="/profile/Jobs/<%= job._id %>/apply" method="POST">
      <input type="hidden" name="applicantId" value="<%= user._id %>" />

      <div>
        <label>Bio:</label>
        <p><%= user.profile.bio || "No bio provided." %></p>
      </div>

      <div>
        <label>Skills:</label>
        <ul>
          <% user.profile.skills.forEach(skill => { %>
          <li><%= skill %></li>
          <% }) %>
        </ul>
      </div>

      <div>
        <label>Resume:</label>
        <% if (user.profile.resume) { %>
        <a href="<%= user.profile.resume %>" target="_blank">View Resume</a>
        <% } else { %>
        <p>No resume uploaded.</p>
        <% } %>
      </div>

      <button type="submit">Submit Application</button>
    </form>
  </body>
</html>
