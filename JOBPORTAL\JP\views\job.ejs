<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Details</title>
    <link
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <%-include("flash")%>
    <div class="container mt-5">
      <!-- Job Details Card -->
      <div class="card shadow">
        <div class="card-body">
          <h1 class="card-title text-primary"><%= job.title %></h1>
          <p class="card-text">
            <strong>Description:</strong> <%= job.description %>
          </p>
          <p class="card-text"><strong>Salary:</strong> ₹<%= job.salary %></p>
          <p class="card-text">
            <strong>Location:</strong> <%= job.location %>
          </p>
          <p class="card-text">
            <strong>Position:</strong> <%= job.position %>
          </p>
          <p class="card-text">
            <strong>Company:</strong> <%= job.company.name %>
          </p>

          <div class="d-flex justify-content-start mt-4">
            <form
              action="/profile/jobs/<%=job.id%>/edit"
              method="get"
              class="mr-2"
            >
              <button class="btn btn-outline-primary">Edit</button>
            </form>
            <form
              action="/profile/jobs/<%=job._id%>?_method=DELETE"
              method="post"
            >
              <button class="btn btn-outline-danger">Delete</button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  </body>
</html>
