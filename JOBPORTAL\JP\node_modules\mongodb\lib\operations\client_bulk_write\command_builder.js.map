{"version": 3, "file": "command_builder.js", "sourceRoot": "", "sources": ["../../../src/operations/client_bulk_write/command_builder.ts"], "names": [], "mappings": ";;;AA2QA,wCAeC;AAzRD,kDAAuD;AA0BvD,gBAAgB;AAChB,MAAa,6BAA6B;IAIxC;;;OAGG;IACH,YAAY,MAAiC,EAAE,OAA+B;QAC5E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,IAAI,UAAU;QACZ,IAAI,gBAAgB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,aAAa;QACX,yDAAyD;QACzD,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,qBAAqB,GAAG,CAAC,CAAC;QAC9B,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;QAC7C,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,CAAC;YAC3B,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACjC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,qBAAqB,CAAC,CAAC;gBAC1C,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC,CAAC;gBAC9D,qBAAqB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAE7D,oBAAoB;QACpB,MAAM,OAAO,GAA2B;YACtC,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI;YACrC,GAAG,EAAE,IAAI,2BAAgB,CAAC,UAAU,CAAC;YACrC,MAAM,EAAE,IAAI,2BAAgB,CAAC,MAAM,CAAC;SACrC,CAAC;QACF,iEAAiE;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,EAAE,CAAC;YAClD,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC;QAC3E,CAAC;QACD,4CAA4C;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACjC,CAAC;QACD,OAAO,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;CACF;AAhED,sEAgEC;AAQD;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CACrC,KAA2B,EAC3B,KAAa,EACU,EAAE;IACzB,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,QAAQ,EAAE,KAAK,CAAC,QAAQ;KACzB,CAAC;IACF,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AATW,QAAA,uBAAuB,2BASlC;AAWF;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CAAC,KAA2B,EAAE,KAAa,EAAY,EAAE;IAC9F,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AAFW,QAAA,uBAAuB,2BAElC;AAEF;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CAAC,KAA4B,EAAE,KAAa,EAAY,EAAE;IAChG,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AAFW,QAAA,wBAAwB,4BAEnC;AAEF;;GAEG;AACH,SAAS,qBAAqB,CAC5B,KAAmD,EACnD,KAAa,EACb,KAAc;IAEd,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;KACrB,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;IACvC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAaD;;;;;GAKG;AACI,MAAM,uBAAuB,GAAG,CACrC,KAA2B,EAC3B,KAAa,EACU,EAAE;IACzB,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,CAAC,CAAC;AALW,QAAA,uBAAuB,2BAKlC;AAEF;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,KAA4B,EAC5B,KAAa,EACU,EAAE;IACzB,OAAO,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC,CAAC;AALW,QAAA,wBAAwB,4BAKnC;AAEF;;GAEG;AACH,SAAS,qBAAqB,CAC5B,KAAmD,EACnD,KAAa,EACb,KAAc;IAEd,MAAM,QAAQ,GAA0B;QACtC,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,UAAU,EAAE,KAAK,CAAC,MAAM;KACzB,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,CAAC;IACD,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;QACvB,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;IAC7C,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAYD;;;;;GAKG;AACI,MAAM,wBAAwB,GAAG,CACtC,KAA4B,EAC5B,KAAa,EACc,EAAE;IAC7B,MAAM,QAAQ,GAA8B;QAC1C,MAAM,EAAE,KAAK;QACb,KAAK,EAAE,KAAK;QACZ,MAAM,EAAE,KAAK,CAAC,MAAM;QACpB,UAAU,EAAE,KAAK,CAAC,WAAW;KAC9B,CAAC;IACF,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;IAC7B,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QACjB,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAjBW,QAAA,wBAAwB,4BAiBnC;AAEF,gBAAgB;AAChB,SAAgB,cAAc,CAAC,KAA8B,EAAE,KAAa;IAC1E,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAChD,KAAK,WAAW;YACd,OAAO,IAAA,+BAAuB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAChD,KAAK,YAAY;YACf,OAAO,IAAA,gCAAwB,EAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;AACH,CAAC"}