@import url('https://fonts.googleapis.com/css2?family=Martel+Sans:wght@600&family=Poppins:wght@300;400;700&display=swap');

* {
    padding: 0;
    margin: 0;
    font-family: 'Martial-sans', sans-serif;
}

body {
    background-color: black;
}

.main {
    background-image: url("assests/bgimage.jpg");
    background-position: center center;
    background-size: max(1200px, 100vw);
    background-repeat: no-repeat;
    height: 644px;
    position: relative;
}

.main .box {
    height: 644px;
    width: 100%;
    opacity: 0.69;
    position: absolute;
    top: 0;
    background-color: black;
}

nav {
    max-width: 78vw;
    justify-content: space-between;
    margin: auto;
    display: flex;
    align-items: center;
    height: 85px;
}

nav img {
    color: red;
    width: 175px;
    position: relative;
    z-index: 10;
}

nav div {
    display: flex;
    gap: 23px;
}

nav .btn {
    position: relative;
    z-index: 10;
}

nav .btn option {
    color: black;
}

.hero {
    font-family: 'Martel Sans', sans-serif;
    height: calc(100% - 85px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: white;
    position: relative;
    gap: 23px;
    padding: 0 30px;
}

.hero> :nth-child(1) {
    font-family: 'Poppins', sans-serif;
    font-weight: bolder;
    font-size: 48px;
    text-align: center;
}

.hero> :nth-child(2) {
    font-weight: 400;
    font-size: 24px;
    text-align: center;
}

.hero> :nth-child(3) {
    font-weight: 400;
    font-size: 20px;
    text-align: center;
}

.separation {
    height: 7px;
    background-color: rgb(46, 44, 44);
    position: relative;
    z-index: 20;
}

.btn-red {
    padding: 3px 8px;
    height: 46px;
    background-color: red;
    color: white;
    font-size: 20px;
    border: none;
    border-radius: 4px;
    font-weight: 400;
}

.btn {
    padding: 3px 11px;
    font-weight: 400;
    width: 110px;
    color: white;
    background-color: rgba(248, 243, 243, 0.021);
    border-radius: 4px;
    border: 1px solid rgb(161, 161, 161);
    cursor: pointer;

}

.btn-red:hover {
    cursor: pointer;
    background-color: rgb(187, 3, 3);
}

.btn-red-sm {
    border: none;
    background-color: red;
    color: white;
}

.btn-red-sm:hover {
    cursor: pointer;
    background-color: rgb(187, 3, 3);
}

.hero-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.main input {
    height: 32px;
    padding: 7px 101px 8px 14px;
    color: white;
    font-size: 12px;
    border-radius: 4px;
    background-color: rgba(23, 23, 23, 0.7);
    border: 1px solid rgba(246, 238, 238, 0.5);
}

.first {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    max-width: 75vw;
    color: white;
    margin: auto;
    margin-top: 60px;
    margin-bottom: 65px;
}

.secimg {
    position: relative;
}

.secimg img {
    position: relative;
    z-index: 10;
    width: 40vw;
}

.secimg video {
    width: 30vw;
    position: absolute;
    top: 77px;
    right: 62px;
}

.first .one {
    font-size: 44px;
    font-weight: 600;
}

.first .two {
    font-size: 21px;
    font-weight: 400;
}

.second {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 75vw;
    color: white;
    margin: auto;
    margin-top: 80px;
    margin-bottom: 110px;
}

.second .one {
    font-size: 38px;
    font-weight: 900;
}

.second .two {
    font-size: 20px;
}

.second img {
    height: 50vh;
    width: 40vw;
}

.third {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    max-width: 75vw;
    color: white;
    margin: auto;
    margin-top: 60px;
    margin-bottom: 65px;
}

.thdimg {
    position: relative;
}

.thdimg img {
    position: relative;
    z-index: 10;
    width: 40vw;
}

.thdimg video {
    width: 24vw;
    position: absolute;
    top: 33px;
    right: 105px;
}

.third .one {
    font-size: 44px;
    font-weight: 600;
}

.third .two {
    font-size: 21px;
    font-weight: 400;
}

.fourth {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 75vw;
    color: white;
    margin: auto;
    margin-top: 50px;
    margin-bottom: 70px;
}

.fourth .one {
    font-size: 40px;
    font-weight: 900;
}

.fourth .two {
    font-size: 18px;
}

.fourth img {
    height: 70vh;
    width: 40vw;
}

.fifth {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 70vw;
    color: white;
    margin: auto;
    margin-top: 50px;
    margin-bottom: 60px;
}

.fifth .heading {
    font-size: 40px;
    font-weight: bolder;
}

.fifth .questions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 8px;
}

.fifth .questions .Qbox {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 35px;
    height: 47px;
    width: 75vw;
    border: none;
    background-color: rgb(41, 40, 40);
    color: white;
    font-size: 20px;
}

.fifth .questions .Qbox:hover {
    background-color: rgb(55, 53, 53);
}

.lastlogin {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 40px;
}

.fifth .lastlogin input {
    height: 32px;
    padding: 7px 101px 8px 14px;
    color: white;
    font-size: 12px;
    border-radius: 4px;
    background-color: rgba(23, 23, 23, 0.7);
    border: 1px solid rgba(246, 238, 238, 0.5);
}

footer {
    height: 70vh;
    width: 75vw;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

footer .first_line {
    display: flex;
    gap: 4px;
}

.first_line span {
    color: rgb(161, 161, 161);
}

.first_line a {
    text-decoration: underline;
    color: rgb(161, 161, 161);
}

.block {
    height: 70vh;
    width: 80vw;
    margin: auto;
    margin-top: 70px;
    margin-bottom: 60px;
    display: flex;
    flex-direction: column;
    gap: 30px;
    font-size: 14px;
    font-weight: 500;
}

.block .list {
    display: flex;
    flex-direction: row;
    gap: 100px;
}

.block .list ul {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.block .list ul li a {
    text-decoration: underline;
    color: rgb(161, 161, 161);
}

.block .lastlang select {
    color: white;
}

.block .lastlang select option {
    color: black;
}