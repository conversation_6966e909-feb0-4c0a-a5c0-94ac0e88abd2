<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Profile - JOBHUNT</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH"
      crossorigin="anonymous"
    />
    <link
      rel="icon"
      sizes="32x32"
      type="image/png"
      href="https://transparentbpo.com/wp-content/uploads/2020/03/favicon-32x32-1.png"
    />
    <style>
      body {
        background-color: #f5f5f5;
      }
      .nav-link {
        color: #007bff;
        font-weight: bold;
      }
      .header,
      .section,
      .footer {
        padding: 40px 0;
      }
      .header {
        background-color: #ffffff;
      }
      .header_main_title {
        font-size: 2.5rem;
        font-weight: 700;
      }
      .header_btn {
        background-color: #007bff;
        color: #fff;
      }
      .featured_card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        transition: box-shadow 0.3s;
      }
      .featured_card:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }
      .team_img {
        max-width: 100%;
        border-radius: 8px;
      }
    </style>
    <style>
  .selected {
    opacity: 1;
    font-weight: bold;
  }
  
  .faded {
    opacity: 0.5;
  }
</style>
  </head>
  <body>
  <nav class="navbar navbar-expand-lg navbar-light bg-secondary">
    <div class="container">
      <a class="navbar-brand font-weight-bold" href="/profile">JOBHUNT</a>
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item"><a href="#" class="nav-link">Contact</a></li>
          <li class="nav-item"><a href="#" class="nav-link">About</a></li>
          <% if (!isAuthenticated) { %>
            <li class="nav-item">
              <a href="/login" class="btn btn-primary ms-3">Login</a>
            </li>
            <li class="nav-item">
              <a href="/register" class="btn btn-primary ms-3">Register</a>
            </li>
          <% } else { %>
            <form action="/logout" method="post" class="d-inline">
              <button class="btn btn-primary ms-3">Logout</button>
            </form>
            <a href="/profile" class="nav-item">
              <img
                src="<%= user && user.profile && user.profile.profilePhoto ? user.profile.profilePhoto : '/default-avatar.png' %>"
                alt="profile"
                class="rounded-circle ms-3"
                width="40"
              />
            </a>
          <% } %>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Profile Section -->
  <div class="container mt-5 row justify-content-center">
    <!-- Profile Picture and Edit Button -->
    <div class="col-md-3 text-center">
      <img
        class="rounded-circle mb-3"
        src="<%= user.profile.profilePhoto %>"
        alt="Profile Photo"
        width="200"
      />
      <button class="btn btn-primary">Edit</button>
    </div>

    <!-- Profile Details -->
    <div class="col-md-6 card p-4">
      <h2 class="text-center"><%= user.username %></h2>
      <p class="text-center"><%= user.email %></p>
      <% if (user.profile && user.profile.skills && user.profile.skills.length) { %>
        <h5>Skills</h5>
        <ul class="list-unstyled">
          <% user.profile.skills.forEach(skill => { %>
            <li><i class="bi bi-check-circle"></i> <%= skill %></li>
          <% }) %>
        </ul>
      <% } %>
      <% if (user.role === "candidate") { %>
        <a href="#" class="d-block mb-2">View Resume</a>
      <% } %>
      <% if (user.profile && user.profile.bio) { %>
        <p><%= user.profile.bio %></p>
      <% } %>
    </div>
  </div>

  <!-- Applied Jobs Section for Candidates -->
  <% if (user.role == "candidate") { %>
    <div class="container mt-5">
      <% if (applications && applications.length > 0) { %>
        <h2 class="text-center mb-4">Applied Jobs</h2>
        <div class="row">
          <% for (let application of applications) { %>
            <% if (application.job) { %>
              <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                  <div class="card-body">
                    <h5 class="card-title"><%= application.job.title %></h5>
                    <p class="card-text">
                      <h5>Status: 
                        <span class="badge 
                          <%= application.status === 'accepted' ? 'bg-success' 
                            : application.status === 'rejected' ? 'bg-danger' 
                            : 'bg-warning text-dark' %>">
                          <%= application.status.charAt(0).toUpperCase() + application.status.slice(1) %>
                        </span>
                      </h5>
                    </p>
                  </div>
                </div>
              </div>
            <% } %>
          <% } %>
        </div>
      <% } else { %>
        <p class="text-center">You have not applied to any jobs yet.</p>
      <% } %>
    </div>
  <% }  else if (user.role=="recruiter") {%>
    <h2>Received Applications</h2>
<% applications.forEach(application => { %>
  <div class="application border p-3 mb-3" data-id="<%= application._id %>">
    <h5>Job Title: <%= application.job.title %></h5>
    <p>Applicant: <%= application.applicant.username %></p>
    <p>Status: 
      <span class="badge 
        <%= application.status === 'accepted' ? 'bg-success' 
          : application.status === 'rejected' ? 'bg-danger' 
          : 'bg-warning text-dark' %>">
        <%= application.status.charAt(0).toUpperCase() + application.status.slice(1) %>
      </span>
    </p>
    
    <!-- Approve and Reject buttons -->
    <form action="/applications/<%= application._id %>/status" method="POST" class="d-inline">
      <input type="hidden" name="status" value="accepted">
      <button type="submit" class="btn btn-success btn-sm status-btn" 
              onclick="handleButtonClick(event, '<%= application._id %>', 'approve')">Approve</button>
    </form>
    <form action="/applications/<%= application._id %>/status" method="POST" class="d-inline">
      <input type="hidden" name="status" value="rejected">
      <button type="submit" class="btn btn-danger btn-sm status-btn" 
              onclick="handleButtonClick(event, '<%= application._id %>', 'reject')">Reject</button>
    </form>
  </div>
<% }) %>



  <%}%>

  <script
    src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz"
    crossorigin="anonymous"
  ></script>
  <script>
  function handleButtonClick(event, applicationId, action) {
    event.preventDefault();

    const applicationElement = document.querySelector(
      `.application[data-id="${applicationId}"]`
    );

    const approveButton = document.querySelector(
      `button[onclick="handleButtonClick(event, '${applicationId}', 'approve')"]`
    );
    const rejectButton = document.querySelector(
      `button[onclick="handleButtonClick(event, '${applicationId}', 'reject')"]`
    );

    if (action === 'approve') {
      approveButton.classList.add('selected');
      rejectButton.classList.add('faded');
    } else {
      rejectButton.classList.add('selected');
      approveButton.classList.add('faded');
    }

    // Submit the form after a slight delay for visual effect
    setTimeout(() => {
      event.target.closest('form').submit();
      if (applicationElement) {
        applicationElement.remove(); // Remove the application element from the DOM
      }
    }, 100);
  }
</script>
</body>

  </body>
</html>
