.logo img {
    width: 164px;
}

nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-family: "Segoe UI";
}

.right ul {
    display: flex;
    gap: 34px;
}

.right ul li {
    list-style: none;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.right ul li span {
    padding: 0 5px;
}

.first {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 23px;
    margin: 70px;
    font-family: sans-serif;
}

.first span {
    font-size: 40px;
}

section span img {
    width: 82px;
    margin: 0 23px;
}

.first p {
    font-size: 23px;
    width: 44vw;
    text-align: center;
}



.btn {
    background: #00b6d1;
    padding: 14px 16px;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 700;
    margin: 12px 0;
    cursor: pointer;
    display: flex;
}
.btn a{
    text-decoration: none;
    color: white;
}

.green {
    background-color: #5cb85c;
}

.flex {
    display: flex;
}

.small {
    font-size: 12px;
}

.second { 
    display: flex;
    flex-direction: column;
    align-items: center; 
}

.item{
    
    margin: 15px;
    padding: 23px;
    gap: 23px;
    background-color: rgb(228, 228, 228);
    font-size: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 10px;
}
.item-lang{
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}
.grid{
    margin-top: 45px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 23px;
}


@media screen and (max-width : 975px) {
    .grid{
        margin-top: 45px;
        display: grid;
        grid-template-columns: 1fr;
        gap: 23px;
    }
}