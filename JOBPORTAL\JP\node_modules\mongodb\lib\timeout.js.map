{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../src/timeout.ts"], "names": [], "mappings": ";;;AAAA,mCAAkD;AAElD,mCAAoD;AACpD,mCAA+B;AAE/B,gBAAgB;AAChB,MAAa,YAAa,SAAQ,KAAK;IACrC,IAAa,IAAI;QACf,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,YAAY,OAAe,EAAE,OAA2B;QACtD,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,KAAc;QACtB,OAAO,CACL,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAC/F,CAAC;IACJ,CAAC;CACF;AAdD,oCAcC;AAID;;;;;KAKK;AACL,MAAa,OAAQ,SAAQ,OAAc;IACzC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACtB,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IASD,yDAAyD;IACzD,YAAoB,WAAqB,GAAG,EAAE,CAAC,IAAI,EAAE,QAAgB,EAAE,KAAK,GAAG,KAAK;QAClF,IAAI,MAAe,CAAC;QAEpB,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,iCAAyB,CAAC,kDAAkD,CAAC,CAAC;QAC1F,CAAC;QAED,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE;YACzB,MAAM,GAAG,aAAa,CAAC;YAEvB,QAAQ,CAAC,YAAI,EAAE,aAAa,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAhBE,UAAK,GAAkB,IAAI,CAAC;QAE5B,aAAQ,GAAG,KAAK,CAAC;QAgBtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,EAAE,GAAG,IAAA,mBAAU,EAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,MAAM,CAAC,IAAI,YAAY,CAAC,iBAAiB,QAAQ,IAAI,CAAC,CAAC,CAAC;YAC1D,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,UAAU,IAAI,KAAK,EAAE,CAAC;gBACjD,uDAAuD;gBACvD,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAA,qBAAY,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;IACtB,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,UAAkB,EAAE,KAAe;QACvD,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,OAAgB;QACxB,OAAO,CACL,OAAO,OAAO,KAAK,QAAQ;YAC3B,OAAO,IAAI,IAAI;YACf,MAAM,CAAC,WAAW,IAAI,OAAO;YAC7B,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,gBAAgB;YAChD,MAAM,IAAI,OAAO;YACjB,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CACnC,CAAC;IACJ,CAAC;CACF;AAhED,0BAgEC"}