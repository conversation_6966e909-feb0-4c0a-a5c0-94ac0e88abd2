const mongoose = require("mongoose");
const express = require("express");
const app = express();
const path = require("path");
const bodyParser = require("body-parser");
const PORT = 8090;
const methodOverride = require("method-override");
app.use(methodOverride("_method"));
const recruiter = require("./router/recruiter.js");
const passport = require("passport");
const LocalStrategy = require("passport-local");
const flash = require("connect-flash");

//sessions
var session = require("express-session");

//models
const Application = require("./models/Application");
const Company = require("./models/Company");
const Job = require("./models/Job");
const User = require("./models/User");

const sessionOptions = {
  secret: "sophisticated",
  resave: false,
  saveUninitialized: true,
  cookie: {
    maxAge: 1000 * 60 * 60 * 24 * 7,
    httpOnly: true,
  },
};
app.use(session(sessionOptions));
app.use(flash());

//passport
app.use(passport.initialize());
app.use(passport.session());
passport.use(new LocalStrategy(User.authenticate()));
passport.serializeUser(User.serializeUser());
passport.deserializeUser(User.deserializeUser());

app.use((req, res, next) => {
  res.locals.success = req.flash("success");
  res.locals.error = req.flash("error");
  next();
});

//router
app.use("/profile", recruiter);

//always used code
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));
app.use(express.static(path.join(__dirname, "public")));
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());

//mongo connected
main().catch((err) => console.log(err));
async function main() {
  await mongoose.connect("mongodb://127.0.0.1:27017/JobPortal");
}

app.get("/ho", (req, res) => {
  res.render("ho");
});

//Home page
app.get("/", (req, res) => {
  
  if(req.user){
   
  res.render("home.ejs", {
    isAuthenticated: req.isAuthenticated(),
    user:req.user // Pass the user object

  });
} else{
  res.render("home.ejs", {
    isAuthenticated: req.isAuthenticated(),
    user:req.user // Pass the user object

  });
}
});

// login signup
app.get("/register", (req, res) => {
  res.render("signUp.ejs");
});

app.post("/register", async (req, res) => {
  const { role, username, email, password } = req.body;
  console.log(role);
  const newUser = new User({ email, username, role });
  const registeredUser = await User.register(newUser, password);
  req.login(registeredUser, () => {});
  console.log(registeredUser);

  req.flash("success", "welcome to jobHunt");
  res.redirect("/profile");
});
//login
app.get("/login", (req, res) => {
  res.render("login.ejs");
});

app.post(
  "/login",
  passport.authenticate("local", {
    failureRedirect: "/login",
    failureFlash: true,
    successRedirect: "/profile",
  }),
  async (req, res) => {
    req.flash("success", "You have successfully logged in!");
    res.redirect("/profile");
  }
);

//logout
app.post("/logout", (req, res) => {
  req.logout(() => {}); // have not done error handeling do passing an empty call back
  req.flash("success", "User logged out successfully");
  res.redirect("/profile");
});

app.get("/pro", async (req, res) => {
  if (!req.user) {
    return res.redirect("/login"); // Redirect if not logged in
  }
  const user = req.user;
  const userId = user._id;
  if (user.role === "candidate") {
    // If the user is a candidate, find applications they submitted
    applications = await Application.find({ applicant: userId })
      .populate("job")
      .populate("applicant");
  } else if (user.role === "recruiter") {
    const jobs = await Job.find({ created_by: userId });
    const jobIds = jobs.map((job) => job._id); // sari jobs ki ids ko nikala

    applications = await Application.find({ job: { $in: jobIds } })
      .populate("job")
      .populate("applicant");
  }

  res.render("pro", {
    isAuthenticated: req.isAuthenticated(),
    user,
    applications,
  });
});

app.post("/applications/:id/status", async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  try {
    // Update application status in the database
    await Application.findByIdAndUpdate(id, { status: status });
    res.redirect("/pro"); // Redirect back to the page with applications
  } catch (error) {
    console.error("Error updating application status:", error);
    res.status(500).send("Server error");
  }
});

// Candidate
app.get("/profile/allJobs", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const userId = req.user._id;
  const user = await User.findById(userId);
  const jobs = await Job.find();

  res.render("jobs.ejs", { jobs, user });
});

app.get("/profile/Jobs/:jobId/apply", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }

  const userId = req.user._id;
  const { jobId } = req.params;

  try {
    const job = await Job.findById(jobId).populate("company");
    if (!job) return res.status(404).send("Job not found");

    const user = await User.findById(userId);
    if (!user) return res.status(401).send("User not authenticated");

    res.render("applyJob", { job, user });
  } catch (err) {
    res.status(500).send("Error displaying job application form.");
  }
});

app.post("/profile/Jobs/:jobId/apply", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const { jobId } = req.params;
  const applicantId = req.user._id;

  try {
    const job = await Job.findById(jobId);
    if (!job) return res.status(404).send("Job not found");
    const application = new Application({
      job: jobId,
      applicant: applicantId,
    });

    await application.save();
    res.redirect("/pro");
  } catch (err) {
    res.status(500).send("Error submitting application.");
  }
});

//Recruiter Profile
// app.get("profile/recruiter/:recruiterId", async (req, res) => {
//   const { recruiterId } = req.params;
//   const user = await User.findById(recruiterId);

//   res.render("recProfile", { user });
// });

//listen
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
