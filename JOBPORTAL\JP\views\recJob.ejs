<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>All Jobs</title>
    <link
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <h1><%=success%></h1>
    <div class="container mt-5">
      <!-- Create New Job Button -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-4">All Jobs</h1>
        <form action="http://localhost:8080/profile/create-job" method="get">
          <button class="btn btn-primary">Create New Job</button>
        </form>
      </div>

      <!-- Jobs List -->
      <% for(let job of jobs) { %>
      <div class="card mb-4">
        <div class="card-body">
          <h2 class="card-title"><%= job.title %></h2>
          <p class="card-text">
            <strong>Description:</strong> <%= job.description %>
          </p>

          <p class="card-text">
            <strong>Company:</strong> <%= job.company.name %>
          </p>
          <a href="/profile/jobs/<%=job.id %>" class="btn btn-outline-info"
            >View Details</a
          >
        </div>
      </div>
      <% } %>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  </body>
</html>
