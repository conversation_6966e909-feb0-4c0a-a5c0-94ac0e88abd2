<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Netflix India - Watch TV Shows Online, Watch Movies Online</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="styles.css" />
  </head>

  <body>
    <div class="main">
      <nav>
        <span><img src="assests/logo2.svg" alt="netflix" /></span>
        <div>
          <select class="btn">
            <option value="english">English</option>
            <option value="hindi">Hindi</option>
          </select>
          <button class="btn btn-red-sm">Sign In</button>
        </div>
      </nav>
      <div class="box"></div>
      <div class="hero">
        <span>Unlimited movies, TV shows and more</span>
        <span>Watch anywhere. Cancel anytime.</span>
        <span
          >Ready to watch? Enter your email to create or restart your
          membership.</span
        >
        <div class="hero-buttons">
          <input type="text" placeholder="Email Address" />
          <button class="btn-red">Get Started &gt;</button>
        </div>
      </div>
      <div class="separation"></div>
    </div>
    <section class="first">
      <div>
        <span class="one">Enjoy on your TV </span>
        <span class="two"
          >Watch on smart TVs, PlayStation, Xbox, Chromecast, Apple TV, Blu-ray
          players and more.</span
        >
      </div>
      <div class="secimg">
        <img src="assests/tv.png" alt="tv" />
        <video src="assests/vedio1.mp4" autoplay loop muted></video>
      </div>
    </section>
    <div class="separation"></div>
    <section class="second">
      <div class="mob">
        <img src="assests/mobile1.jpg" alt="mobile" />
      </div>
      <div>
        <div class="one">Download your shows to watch offline</div>
        <div class="two">
          Save your favourites easily and always have something to watch
        </div>
      </div>
    </section>
    <div class="separation"></div>
    <section class="third">
      <div>
        <span class="one">Enjoy on your TV </span>
        <span class="two"
          >Watch on smart TVs, PlayStation, Xbox, Chromecast, Apple TV, Blu-ray
          players and more.</span
        >
      </div>
      <div class="thdimg">
        <img src="assests/tv2.png" alt="tv" />
        <video src="assests/vedio2.mp4" autoplay loop muted></video>
      </div>
    </section>
    <div class="separation"></div>
    <section class="fourth">
      <div class="mob">
        <img src="assests/children.png" alt="kids" />
      </div>
      <div>
        <div class="one">Create profiles for kids</div>
        <div class="two">
          Send children on adventures with their favourite characters in a space
          made just for them—free with your membership.
        </div>
      </div>
    </section>
    <div class="separation"></div>
    <section class="fifth">
      <div class="heading">Frequently Asked Questions</div>
      <div class="questions">
        <button class="Qbox">
          <span>What is Netflix?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
        <button class="Qbox">
          <span>How much does Netflix cost?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
        <button class="Qbox">
          <span>Where can I watch?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
        <button class="Qbox">
          <span>How do I Cancel?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
        <button class="Qbox">
          <span>What can I watch on Netflix?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
        <button class="Qbox">
          <span>Is Netflix good for kids?</span
          ><svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            width="36"
            height="36"
            viewBox="0 0 36 36"
            role="img"
            data-icon="PlusLarge"
            aria-hidden="true"
            class="elj7tfr3 default-ltr-cache-1dpnjn e164gv2o4"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17 17V3H19V17H33V19H19V33H17V19H3V17H17Z"
              fill="currentColor"
            ></path>
          </svg>
        </button>
      </div>
      <div class="lastlogin">
        <span
          >Ready to watch? Enter your email to create or restart your
          membership.</span
        >
        <div class="hero-buttons">
          <input type="text" placeholder="Email Address" />
          <button class="btn-red">Get Started &gt;</button>
        </div>
      </div>
    </section>
    <div class="separation"></div>
    <footer>
      <div class="block">
        <div class="first_line">
          <span>Questions?call</span>
          <span><a href="tel:000-************">000-************</a></span>
        </div>
        <div class="list">
          <ul type="none" id="firstcolumn" class="link">
            <li><a href="https://help.netflix.com/en/node/412">FAQ</a></li>
            <li>
              <a href="https://ir.netflix.net/ir-overview/profile/default.aspx"
                >Investor Relation</a
              >
            </li>
            <li>
              <a href="https://help.netflix.com/legal/privacy">Privacy</a>
            </li>
            <li><a href="https://fast.com/">Speed Test</a></li>
          </ul>
          <ul type="none" id="secondcolumn" class="link">
            <li><a href="https://help.netflix.com/en">Help Centre</a></li>
            <li><a href="https://jobs.netflix.com/">Jobs</a></li>
            <li><a href="">Cookie Prefrences</a></li>
            <li>
              <a href="https://help.netflix.com/legal/notices">Legal Notices</a>
            </li>
          </ul>
          <ul type="none" id="thirdcolumn" class="link">
            <li>
              <a
                href="https://www.netflix.com/login?nextpage=https%3A%2F%2Fwww.netflix.com%2Fyouraccount"
                >Account</a
              >
            </li>
            <li>
              <a href="https://help.netflix.com/en/node/14361">Ways to Watch</a>
            </li>
            <li>
              <a href="https://help.netflix.com/en/node/134094"
                >Coperate Information</a
              >
            </li>
            <li>
              <a href="https://www.netflix.com/in/browse/genre/839338"
                >Only on Netflix</a
              >
            </li>
          </ul>
          <ul type="none" id="fourthcolumn" class="link">
            <li><a href="https://media.netflix.com/en/">Media Centre</a></li>
            <li>
              <a href="https://help.netflix.com/legal/termsofuse"
                >Terms of use</a
              >
            </li>
            <li>
              <a href="https://help.netflix.com/en/contactus">Contact Us</a>
            </li>
          </ul>
        </div>
        <div class="lastlang">
          <select class="btn">
            <option value="english">English</option>
            <option value="hindi">Hindi</option>
          </select>
        </div>
        <div style="color: rgb(161, 161, 161)">Netflix India</div>
      </div>
    </footer>
  </body>
</html>