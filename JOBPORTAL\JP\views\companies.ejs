<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>List of Companies</title>
    <link
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container mt-5 text-center">
      <h1 class="display-3 font-weight-bold text-primary mb-4">
        <%= user.username %>'s Companies
      </h1>
    </div>
    <div class="container mt-5">
      <!-- Create New Company Button -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-4">List of Companies</h1>
        <form action="/profile/companies/new" method="get">
          <button class="btn btn-primary">Create New Company</button>
        </form>
      </div>

      <% if (companies.length > 0) { %> <% for (let company of companies) { %>
      <div class="card mb-4" style="border: 2px solid blue; height: 100%">
        <div class="card-body">
          <h5 class="card-title"><%= company.name %></h5>
          <p class="card-text">
            <strong>Location:</strong> <%= company.location %>
          </p>
          <a href="/companies/<%= company._id %>" class="btn btn-outline-info">
            View Details
          </a>
        </div>
      </div>
      <% } %> <% } else { %>
      <p class="text-center text-muted">No companies found.</p>
      <% } %>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  </body>
</html>
