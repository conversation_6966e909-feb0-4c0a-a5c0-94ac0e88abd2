<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>saimon-says</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <h1>Simon Game</h1>
    <h2>Press any key to start the game</h2>
    <div class="btn-containers">
      <div class="lineOne">
        <div class="btn red" type="button" id="red">1</div>
        <div class="btn green" type="button" id="green">2</div>
      </div>
      <div class="lineTwo">
        <div class="btn yellow" type="button" id="yellow">3</div>
        <div class="btn purple" type="button" id="purple">4</div>
      </div>
    </div>
    <script src="Simon.js"></script>
  </body>
</html>
