const express = require("express");
const router = express.Router();
const methodOverride = require("method-override");
router.use(methodOverride("_method"));
router.use(express.json());
router.use(express.urlencoded({ extended: true }));
//models
const Application = require("../models/Application");
const Company = require("../models/Company");
const Job = require("../models/Job");
const User = require("../models/User");

router.get("/companies", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }

  try {
    const user = await User.findById(req.user._id).populate("profile.company");
    console.log("User:", user);
    const companies = user.profile ? user.profile.company : [];
    console.log("Companies:", companies);

    res.render("companies", { companies, user });
  } catch (err) {
    console.error("Error retrieving companies:", err);
    res.status(500).send("Error retrieving companies");
  }
});

//register a company
router.get("/companies/new", (req, res) => {
  res.render("newCompany.ejs");
});

router.post("/companies", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const { name, location, website, description } = req.body;
  const userId = req.user._id;
  const newCompany = new Company({
    name,
    description,
    website,
    location,
    userId,
  });
  await newCompany.save();

  await User.findByIdAndUpdate(userId, {
    $push: { "profile.company": newCompany._id },
  });
  res.redirect("/profile/companies");
});
//GET JOB
router.get("/create-job", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const recruiterId = req.user._id;

  try {
    // Find companies owned by the recruiter
    const companies = await Company.find({ userId: recruiterId });
    for (let comp of companies) {
      console.log(comp.name);
    }
    if (!companies.length) {
      return res.status(404).send("No companies found for this recruiter.");
    }
    // Render the job creation form, passing the companies list
    res.render("newJob", { companies, recruiterId });
  } catch (err) {
    res.status(500).send("Error loading job creation form.");
  }
});

//POST JOB
router.post("/jobs", async (req, res) => {
  const recruiterId = req.user._id;
  try {
    const {
      title,
      description,
      requirements,
      salary,
      location,
      jobType,
      position,
      company, // company ID from the dropdown
    } = req.body;
    const newJob = new Job({
      title,
      description,
      requirements,
      salary,
      location,
      jobType,
      position,
      company, // company ID selected from the dropdown
      created_by: recruiterId, // reference to recruiter
    });
    await newJob.save();
    req.flash("success", "New Job created!");
    res.redirect(`/profile/jobs`); // Redirect after successful job creation
  } catch (error) {
    console.error(error);
  }
});

router.get("/jobs", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  try {
    const recruiterId = req.user._id;
    const companies = await Company.find({ userId: recruiterId });
    const companyIds = companies.map((company) => company._id);
    const jobs = await Job.find({ company: { $in: companyIds } }).populate(
      "company"
    );
    res.render("recJob", { jobs, recruiterId });
  } catch (error) {
    console.error("Error fetching jobs for recruiter:", error);
    res.status(500).json({ error: "Failed to retrieve jobs" });
  }
});

// get job route
router.get("/jobs/:jobId", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const { jobId } = req.params;
  const recruiterId = req.user._id;
  const job = await Job.findById(jobId);
  res.render("job", { job, recruiterId });
});

// Edit route
router.get("/jobs/:jobId/edit", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const recruiterId = req.user._id;
  const company = await Company.findOne({ userId: recruiterId });
  const jobId = req.params.jobId;
  const job = await Job.findById(jobId);
  res.render("editJob", { job, recruiterId, company });
});

//update job
router.put("/jobs/:jobId", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const recruiterId = req.user._id;
  const { jobId } = req.params;
  const job = await Job.findById(jobId).populate("company");
  const {
    title,
    description,
    requirements,
    salary,
    location,
    jobType,
    position,
  } = req.body;

  const updatedJob = await Job.findByIdAndUpdate(jobId, {
    title,
    description,
    requirements,
    salary,
    location,
    jobType,
    position,
  });
  console.log(updatedJob);
  req.flash("success", "Job edited successfully");
  res.redirect(`/profile/jobs/${updatedJob._id}`);
});

//DELETE
router.delete("/jobs/:jobId", async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.redirect("/login");
  }
  const recruiterId = req.user._id;
  const { jobId } = req.params;
  const delJob = await Job.findOneAndDelete({ _id: jobId });
  console.log(delJob);
  res.redirect(`/profile/jobs`);
});

module.exports = router;
