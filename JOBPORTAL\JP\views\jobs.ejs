<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Listings</title>
    <link
      href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <style>
      .job-box {
        border: 2px solid #007bff; /* Bootstrap primary blue color */
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body class="bg-light">
    <div class="container my-5">
      <% for(let job of jobs) { %>
      <div class="job-box bg-white shadow-sm">
        <h1 class="h4"><%= job.title %></h1>
        <p><%= job.description %></p>
        <p class="text-muted"><strong>Salary:</strong> <%= job.salary %></p>
        <p class="text-muted"><strong>Location:</strong> <%= job.location %></p>
        <p class="text-muted"><strong>Position:</strong> <%= job.position %></p>

        <% if(user.role == "candidate") { %>
        <form action="/profile/Jobs/<%= job.id %>/apply" method="get">
          <button type="submit" class="btn btn-primary mt-3">Apply</button>
        </form>
        <% } %>
      </div>
      <% } %>
    </div>
  </body>
</html>
