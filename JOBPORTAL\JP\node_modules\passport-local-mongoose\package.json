{"name": "passport-local-mongoose", "description": "Mongoose plugin that simplifies building username and password login with Passport", "version": "8.0.0", "main": "index.js", "repository": "saint<PERSON><PERSON><PERSON>/passport-local-mongoose", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["mongoose", "passport", "authentication", "login"], "engines": {"node": ">= 8.0.0"}, "dependencies": {"generaterr": "^1.5.0", "passport-local": "^1.0.0", "scmp": "^2.1.0"}, "devDependencies": {"@types/passport": "1.0.7", "@types/passport-local": "1.0.34", "chai": "^4.3.6", "debug": "^4.3.4", "drop-mongodb-collections": "^2.0.0", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "mocha-lcov-reporter": "^1.3.0", "mongoose": "^7.0.1", "nyc": "^15.1.0", "prettier": "^2.6.2", "should-release": "^1.2.0", "standard-version": "^9.5.0", "tsd": "^0.20.0"}, "scripts": {"test": "nyc --reporter=text-summary mocha --recursive --throw-deprecation", "test:ci": "nyc --reporter=lcov mocha --recursive --throw-deprecation", "test:tsd": "tsd", "lint": "eslint .", "lint:fix": "eslint . --fix", "should-release": "should-release", "release": "standard-version"}, "tsd": {"directory": "test/types"}}