<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Weather App</title>
    <link rel="stylesheet" href="style.css">
  </head>
  <body>
    <h1 class="title"><PERSON><PERSON>'s Weather App</h1>
    <div class="container">
      <div class="weather_container">
        <div class="temp">
          <p>45°C</p>
        </div>
        <div class="location">
          <p>Delhi</p>
        </div>
        <div class="time">
          <p>2025-06-10 09:44</p>
        </div>
        <div class="condition">
          <p class = "condition-text">Hot</p>
        </div>
      </div>
    </div>
    <nav>
        <form>
            <input type="text"  placeholder="Search for a location" class="search_field"/>
            <button type="submit">Search</button>
        </form>
    </nav>
  </body>
  <script src="script.js"></script>
</html>
