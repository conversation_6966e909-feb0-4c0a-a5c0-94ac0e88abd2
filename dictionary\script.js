const input = document.querySelector('input')
const btn = document.querySelector('button')
let url = "https://api.dictionaryapi.dev/api/v2/entries/en/<word>"
const dictionary = document.querySelector('main')

async function getWord(word){
    const res = await fetch(url.replace('<word>', word))
    const data = await res.json()
    return data
}

btn.addEventListener('click', updatedata)

let partOfSpeechArray = []
for(let i = 0; i<data.meanings.length-1; i++){
    partOfSpeechArray.push(data.meanings[i].partOfSpeech)
}

async function updatedata(){
    const data = await getWord(input.value)
   // console.log(data)
    dictionary.innerHTML = `
    <div class="word"><h3>word : ${data.word}</h3></div>
        <div class="phonetic"><h3>Phonetic : ${data.phonetic}</h3></div>
        <div class="audio">
          <audio controls src=${data.phonetics[0].audio}></audio>
        </div>
        <div class="definition"><h3>definition : ${data.meanings[0].definitions[0].definition}</h3></div>
        <div class="example"><h3>eg. : ${data.meanings[0].definitions[0].example}</h3></div>
        <div class="part_of_speech"><h3>Part of Speech :${partOfSpeechArray.map(item => item).join(', ')}</h3></div>`
}