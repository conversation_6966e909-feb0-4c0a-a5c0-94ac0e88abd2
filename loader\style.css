*{
    margin: 0;
    padding: 0;
}

body{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
}

.box{
    width: 200px;
    height: 200px;
    background-color: rgb(237, 237, 237);
}

svg{
    height: 200px;
    width: 200px;
}

.loader{
    animation : load 1.3s infinite;
}

@keyframes load{
    0%{
        stroke-dasharray: 10, 510;
        stroke-dashoffset: 0;
    }
    50%{
        stroke-dasharray: 170, 510;
    }
    100%{
        stroke-dasharray: 170, 510;
        stroke-dashoffset: -510;
    }
}
