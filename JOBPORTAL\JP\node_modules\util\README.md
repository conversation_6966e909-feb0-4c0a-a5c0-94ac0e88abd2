# util

[![Build Status](https://travis-ci.org/defunctzombie/node-util.png?branch=master)](https://travis-ci.org/defunctzombie/node-util)

node.js [util](http://nodejs.org/api/util.html) module as a module

## install via [npm](npmjs.org)

```shell
npm install util
```

## browser support

This module also works in modern browsers. If you need legacy browser support you will need to polyfill ES5 features.
