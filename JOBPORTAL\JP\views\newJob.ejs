<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Create Job</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card shadow">
            <div class="card-header bg-primary text-white">
              <h3 class="text-center">Create New Job</h3>
            </div>
            <div class="card-body">
              <form action="http://localhost:8080/profile/jobs" method="POST">
                <div class="mb-3">
                  <label for="title" class="form-label">Job Title</label>
                  <input
                    type="text"
                    name="title"
                    class="form-control"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="description" class="form-label"
                    >Description</label
                  >
                  <textarea
                    name="description"
                    class="form-control"
                    rows="3"
                    required
                  ></textarea>
                </div>
                <div class="mb-3">
                  <label for="requirements" class="form-label"
                    >Requirements</label
                  >
                  <input type="text" name="requirements" class="form-control" />
                </div>
                <div class="mb-3">
                  <label for="salary" class="form-label">Salary</label>
                  <input
                    type="number"
                    name="salary"
                    class="form-control"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="location" class="form-label">Location</label>
                  <input
                    type="text"
                    name="location"
                    class="form-control"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="jobType" class="form-label">Job Type</label>
                  <input
                    type="text"
                    name="jobType"
                    class="form-control"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="position" class="form-label">Position</label>
                  <input
                    type="text"
                    name="position"
                    class="form-control"
                    required
                  />
                </div>
                <div class="mb-3">
                  <label for="company" class="form-label">Company</label>
                  <select name="company" class="form-select" required>
                    <% companies.forEach(company => { %>
                    <option value="<%= company._id %>">
                      <%= company.name %>
                    </option>
                    <% }); %>
                  </select>
                </div>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary btn-lg">
                    Create Job
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS (optional) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
